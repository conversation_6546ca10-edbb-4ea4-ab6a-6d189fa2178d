import { GoogleGenerativeAI } from '@google/generative-ai';
import config from '../config';
import AppError from '../errors/AppError';
import httpStatus from 'http-status';
import { Category } from '../modules/Category/category.model';
import { SubCategory } from '../modules/SubCategory/subCategory.model';

class AIService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    if (!config.gemini_api_key) {
      throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, 'Gemini API key not configured');
    }
    this.genAI = new GoogleGenerativeAI(config.gemini_api_key);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
  }

  async enhanceTitle(originalTitle: string): Promise<string> {
    try {
      const prompt = `
        You are an expert course title optimizer for an online learning platform like Udemy.
        
        Original title: "${originalTitle}"
        
        Create an improved, professional course title that:
        - Is engaging and clear
        - Includes relevant keywords for SEO
        - Appeals to potential students
        - Follows online course best practices
        - Is between 40-60 characters
        - Maintains the core subject matter
        
        Return only the improved title, nothing else.
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text().trim();
    } catch (error) {
      console.error('AI title enhancement error:', error);
      throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to enhance title');
    }
  }

  async enhanceSubtitle(title: string, originalSubtitle?: string): Promise<string> {
    try {
      const prompt = `
        You are an expert course subtitle writer for an online learning platform.
        
        Course title: "${title}"
        ${originalSubtitle ? `Current subtitle: "${originalSubtitle}"` : ''}
        
        Create a compelling course subtitle that:
        - Complements the title perfectly
        - Highlights key benefits or outcomes
        - Is 60-120 characters long
        - Appeals to the target audience
        - Uses action-oriented language
        - Mentions skill level if appropriate
        
        Return only the subtitle, nothing else.
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text().trim();
    } catch (error) {
      console.error('AI subtitle enhancement error:', error);
      throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to enhance subtitle');
    }
  }

  async enhanceDescription(title: string, subtitle?: string, originalDescription?: string): Promise<string> {
    try {
      const prompt = `
        You are an expert course description writer for an online learning platform.
        
        Course title: "${title}"
        ${subtitle ? `Subtitle: "${subtitle}"` : ''}
        ${originalDescription ? `Current description: "${originalDescription}"` : ''}
        
        Create a compelling course description that:
        - Clearly explains what students will learn
        - Highlights key benefits and outcomes
        - Uses bullet points for key features
        - Includes target audience information
        - Mentions prerequisites if any
        - Is 200-400 words long
        - Uses persuasive but professional language
        - Follows online course marketing best practices
        
        Format with proper paragraphs and bullet points where appropriate.
        Return only the description, nothing else.
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text().trim();
    } catch (error) {
      console.error('AI description enhancement error:', error);
      throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to enhance description');
    }
  }

  async suggestCategory(title: string, description?: string): Promise<{
    categoryId: string;
    subcategoryId: string;
    confidence: number;
  }> {
    try {
      const categories = await Category.find({ isActive: true }).select('_id name slug');
      const subcategories = await SubCategory.find({ isActive: true })
        .populate('categoryId', 'name slug')
        .select('_id categoryId name slug');

      const categoryList = categories.map(cat => `${cat.name} (${cat.slug})`).join(', ');
      const subcategoryList = subcategories.map(sub => 
        `${sub.name} (${sub.slug}) - under ${(sub.categoryId as any).name}`
      ).join(', ');

      const prompt = `
        You are an expert course categorization system for an online learning platform.
        
        Course title: "${title}"
        ${description ? `Description: "${description}"` : ''}
        
        Available categories: ${categoryList}
        
        Available subcategories: ${subcategoryList}
        
        Analyze the course content and suggest the most appropriate category and subcategory.
        Consider the subject matter, target audience, and learning objectives.
        
        Respond in this exact JSON format:
        {
          "categorySlug": "category-slug",
          "subcategorySlug": "subcategory-slug",
          "confidence": 0.95,
          "reasoning": "Brief explanation"
        }
        
        Return only valid JSON, nothing else.
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const jsonResponse = JSON.parse(response.text().trim());

      const category = categories.find(cat => cat.slug === jsonResponse.categorySlug);
      const subcategory = subcategories.find(sub => 
        sub.slug === jsonResponse.subcategorySlug && 
        sub.categoryId.toString() === category?._id.toString()
      );

      if (!category || !subcategory) {
        throw new AppError(httpStatus.BAD_REQUEST, 'Invalid category suggestion from AI');
      }

      return {
        categoryId: category._id.toString(),
        subcategoryId: subcategory._id.toString(),
        confidence: jsonResponse.confidence || 0.8
      };
    } catch (error) {
      console.error('AI category suggestion error:', error);
      throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to suggest category');
    }
  }

  async generateCourseOutline(title: string, description?: string, level?: string): Promise<string[]> {
    try {
      const prompt = `
        You are an expert curriculum designer for online courses.
        
        Course title: "${title}"
        ${description ? `Description: "${description}"` : ''}
        ${level ? `Level: ${level}` : ''}
        
        Create a comprehensive course outline with 8-12 main topics/modules that:
        - Follow a logical learning progression
        - Are appropriate for the specified level
        - Cover the subject comprehensively
        - Each topic is 3-8 words long
        - Are actionable and specific
        
        Return as a JSON array of strings, nothing else.
        Example: ["Introduction to Basics", "Core Concepts", "Practical Applications"]
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return JSON.parse(response.text().trim());
    } catch (error) {
      console.error('AI course outline error:', error);
      throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to generate course outline');
    }
  }
}

export const aiService = new AIService();

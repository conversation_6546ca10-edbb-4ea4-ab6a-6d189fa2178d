import React, { useState, use<PERSON><PERSON>back, useEffect, useMemo, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  Check,
  ArrowLeft,
  ArrowRight,
  CloudUpload,
  X,
  Loader2,
  ExternalLink,
  BookOpen,
  DollarSign,
  Settings,
  Eye,
  Upload,
  Sparkles,
  Target,
  Globe,
  Clock,
  Star,
  TrendingUp,
} from "lucide-react";
import { z } from "zod";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { debounce } from "lodash";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from "@/components/ui/switch";
import { StableFileUpload } from "@/components/ui/stable-file-upload";
import { useAppDispatch } from "@/redux/hooks";
import { useGetMeQuery } from "@/redux/features/auth/authApi";
import { useCreateCourseMutation } from "@/redux/features/course/courseApi";
import { COURSE_LEVEL } from "@/types";
import { setCourse } from "@/redux/features/course/courseSlice";
import { CategorySelector } from "@/components/ui/category-selector";
import { AIEnhancementField } from "@/components/ui/ai-enhancement-button";
import {
  useEnhanceTitleMutation,
  useEnhanceSubtitleMutation,
  useEnhanceDescriptionMutation,
  useSuggestCategoryMutation
} from "@/redux/features/ai/aiApi";
import { useGetAllCategoriesWithSubcategoriesQuery } from "@/redux/features/category/categoryApi";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { toast } from "sonner";
import { useConnectStripeAccountMutation } from "@/redux/features/payment/payment.api";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

const courseSchema = z
  .object({
    // Step 1: Course Basics
    title: z
      .string()
      .min(5, { message: "Title must be at least 5 characters" })
      .max(100, { message: "Title must be less than 100 characters" }),
    subtitle: z
      .string()
      .max(120, { message: "Subtitle must be less than 120 characters" })
      .optional(),
    description: z
      .string()
      .min(50, { message: "Description must be at least 50 characters" })
      .max(2000, { message: "Description must be less than 2000 characters" }),
    categoryId: z
      .string()
      .min(1, { message: "Category is required" }),
    subcategoryId: z
      .string()
      .min(1, { message: "Subcategory is required" }),
    courseLevel: z.enum(COURSE_LEVEL),

    // Step 2: Course Content
    courseThumbnail: z.instanceof(File).optional(),
    learningObjectives: z
      .array(z.string().min(1))
      .min(3, { message: "At least 3 learning objectives are required" })
      .max(8, { message: "Maximum 8 learning objectives allowed" })
      .optional(),
    prerequisites: z.string().optional(),
    targetAudience: z.string().optional(),

    // Step 3: Pricing & Settings
    status: z.enum(["draft", "published"]),
    coursePrice: z.string().optional(),
    isFree: z.enum(["free", "paid"]),
    estimatedDuration: z.string().optional(),
    language: z.string().default("English"),
    hasSubtitles: z.boolean().default(false),
    hasCertificate: z.boolean().default(true),
  })
  .superRefine((data, ctx) => {
    if (data.isFree === "paid") {
      if (!data.coursePrice || parseFloat(data.coursePrice) <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Valid price is required for paid courses",
          path: ["coursePrice"],
        });
      }
    }
  });

const steps = [
  {
    id: "basics",
    title: "Course Basics",
    description: "Essential course information",
    icon: BookOpen
  },
  {
    id: "content",
    title: "Course Content",
    description: "Media and learning objectives",
    icon: Upload
  },
  {
    id: "settings",
    title: "Pricing & Settings",
    description: "Price, language, and features",
    icon: Settings
  },
  {
    id: "review",
    title: "Review & Publish",
    description: "Final review and publication",
    icon: Eye
  },
];

const CourseCreate = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { data } = useGetMeQuery(undefined);
  const [createCourse, { isLoading }] = useCreateCourseMutation();

  // State management
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [formInitialized, setFormInitialized] = useState(false);
  const [showStripeModal, setShowStripeModal] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [learningObjectives, setLearningObjectives] = useState<string[]>(['']);

  // API hooks
  const [connectStripeAccount, { isLoading: isConnectingStripe }] = useConnectStripeAccountMutation();
  const [enhanceTitle, { isLoading: isEnhancingTitle }] = useEnhanceTitleMutation();
  const [enhanceSubtitle, { isLoading: isEnhancingSubtitle }] = useEnhanceSubtitleMutation();
  const [enhanceDescription, { isLoading: isEnhancingDescription }] = useEnhanceDescriptionMutation();
  const [suggestCategory, { isLoading: isSuggestingCategory }] = useSuggestCategoryMutation();

  const { data: categoriesData } = useGetAllCategoriesWithSubcategoriesQuery();
  const categories = useMemo(() => categoriesData?.data || [], [categoriesData?.data]);
  const teacherId = data?.data?._id;

  // Form setup
  const form = useForm<z.infer<typeof courseSchema>>({
    resolver: zodResolver(courseSchema),
    defaultValues: {
      status: "draft",
      isFree: "free",
      coursePrice: "",
      language: "English",
      hasSubtitles: false,
      hasCertificate: true,
    },
  });

  // Store form reference for stable access
  const formRef = React.useRef(form);
  formRef.current = form;

  // Watch individual form values to prevent infinite loops
  const categoryId = useWatch({ control: form.control, name: 'categoryId' });
  const subcategoryId = useWatch({ control: form.control, name: 'subcategoryId' });
  const title = useWatch({ control: form.control, name: 'title' });
  const subtitle = useWatch({ control: form.control, name: 'subtitle' });
  const description = useWatch({ control: form.control, name: 'description' });
  const status = useWatch({ control: form.control, name: 'status' });
  const isFree = useWatch({ control: form.control, name: 'isFree' });
  const coursePrice = useWatch({ control: form.control, name: 'coursePrice' });

  // Memoized category names to prevent recalculation
  const selectedCategoryName = useMemo(() => {
    const category = categories.find(cat => cat._id === categoryId);
    return category?.name || 'Not selected';
  }, [categories, categoryId]);

  const selectedSubcategoryName = useMemo(() => {
    const category = categories.find(cat => cat._id === categoryId);
    const subcategory = category?.subcategories?.find(sub => sub._id === subcategoryId);
    return subcategory?.name || 'Not selected';
  }, [categories, categoryId, subcategoryId]);

  // Check if the screen is mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  // Check multiple fields to determine if Stripe is connected
  const hasStripeConnected = useMemo(() => {
    return data?.data?.stripeVerified ||
           data?.data?.stripeOnboardingComplete ||
           (data?.data?.stripeAccountId && data?.data?.stripeAccountId.length > 0);
  }, [data?.data?.stripeVerified, data?.data?.stripeOnboardingComplete, data?.data?.stripeAccountId]);

  // Progress calculation
  const progressPercentage = useMemo(() => {
    const totalSteps = steps.length;
    const completedCount = completedSteps.length;
    const currentProgress = (currentStep + 1) / totalSteps;
    const completedProgress = completedCount / totalSteps;
    return Math.max(currentProgress, completedProgress) * 100;
  }, [currentStep, completedSteps.length]);

  // Load saved form data and initialize
  useEffect(() => {
    const initializeForm = () => {
      try {
        const savedData = localStorage.getItem("courseForm");
        if (savedData) {
          const parsedData = JSON.parse(savedData);

          // Remove non-serializable data
          if (parsedData.courseThumbnail) {
            delete parsedData.courseThumbnail;
          }

          // Handle learning objectives separately
          if (parsedData.learningObjectives && Array.isArray(parsedData.learningObjectives)) {
            setLearningObjectives(parsedData.learningObjectives);
            delete parsedData.learningObjectives;
          }

          // Reset form with cleaned data
          formRef.current.reset(parsedData);
        }
      } catch (e) {
        console.error("Failed to parse saved form data", e);
        // Clear corrupted data
        localStorage.removeItem("courseForm");
      } finally {
        setFormInitialized(true);
      }
    };

    // Use setTimeout to ensure form is ready
    const timeoutId = setTimeout(initializeForm, 100);

    return () => clearTimeout(timeoutId);
  }, []); // Remove form dependency to prevent re-initialization

  // Manual save function for when user navigates between steps
  const saveFormData = useCallback(() => {
    if (!formInitialized) return;

    const values = formRef.current.getValues();
    const serializableValues = {
      ...values,
      learningObjectives: learningObjectives.filter(obj => obj.trim() !== '')
    };

    if (values.courseThumbnail) {
      delete serializableValues.courseThumbnail;
    }

    localStorage.setItem("courseForm", JSON.stringify(serializableValues));
  }, [formInitialized, learningObjectives]);

  // Navigation functions
  const goToNextStep = useCallback(() => {
    saveFormData(); // Save before navigating
    setCompletedSteps((prev) => {
      if (!prev.includes(currentStep)) {
        return [...prev, currentStep];
      }
      return prev;
    });
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  }, [currentStep, saveFormData]);

  const goToPrevStep = useCallback(() => {
    saveFormData(); // Save before navigating
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  }, [saveFormData]);

  const goToStep = useCallback((stepIndex: number) => {
    if (stepIndex <= currentStep + 1 || completedSteps.includes(stepIndex)) {
      saveFormData(); // Save before navigating
      setCurrentStep(stepIndex);
    }
  }, [currentStep, completedSteps, saveFormData]);

  // Learning objectives handlers
  const addLearningObjective = useCallback(() => {
    if (learningObjectives.length < 8) {
      setLearningObjectives(prev => [...prev, '']);
    }
  }, [learningObjectives.length]);

  const removeLearningObjective = useCallback((index: number) => {
    if (learningObjectives.length > 1) {
      setLearningObjectives(prev => prev.filter((_, i) => i !== index));
    }
  }, [learningObjectives.length]);

  const updateLearningObjective = useCallback((index: number, value: string) => {
    setLearningObjectives(prev =>
      prev.map((obj, i) => i === index ? value : obj)
    );
  }, []);

  // Stripe connection handler
  const handleConnectStripe = useCallback(async () => {
    try {
      if (!teacherId) {
        toast.error("Teacher ID is missing. Please try again or contact support.");
        return;
      }

      if (hasStripeConnected) {
        toast.success("Your Stripe account is already connected!");
        setShowStripeModal(false);
        navigate("/teacher/courses");
        return;
      }

      const result = await connectStripeAccount(teacherId).unwrap();

      if (result?.data?.url || result?.url) {
        const url = result?.data?.url || result?.url;
        window.open(url, "_blank");
        toast.info("Completing your Stripe setup in a new tab. Please complete all steps.");
        setShowStripeModal(false);
      } else if (result?.status === "complete") {
        toast.success("Your Stripe account is already set up and verified!");
        setShowStripeModal(false);
      } else {
        toast.warning("Received an unexpected response. Please try again.");
      }
    } catch (error: unknown) {
      const err = error as {
        status?: number;
        data?: { message?: string };
        error?: string
      };

      if (err.data?.message?.includes("Stripe")) {
        toast.error(err.data.message);
      } else if (err.status === 404) {
        toast.error("Teacher account not found. Please contact support.");
      } else if (err.status === 401) {
        toast.error("Authentication error. Please log in again.");
      } else if (err.status === 403) {
        toast.error("You don't have permission to connect a Stripe account.");
      } else if (err.status === 500) {
        toast.error("Server error. Please try again later or contact support.");
      } else {
        toast.error(err.data?.message || "Failed to connect Stripe account");
      }
    }
  }, [teacherId, hasStripeConnected, connectStripeAccount, navigate]);

  // AI Enhancement handlers
  const handleEnhanceTitle = useCallback(async () => {
    if (!title?.trim()) {
      toast.error('Please enter a title first');
      return;
    }

    try {
      const result = await enhanceTitle({ title }).unwrap();
      if (result?.data?.enhancedTitle) {
        formRef.current.setValue('title', result.data.enhancedTitle);
        toast.success('Title enhanced successfully!');
      } else {
        toast.error('No enhancement received');
      }
    } catch (error) {
      console.error('Title enhancement error:', error);
      toast.error('Failed to enhance title');
    }
  }, [title, enhanceTitle]);

  const handleEnhanceSubtitle = useCallback(async () => {
    if (!title?.trim()) {
      toast.error('Please enter a title first');
      return;
    }

    try {
      const result = await enhanceSubtitle({
        title,
        subtitle: subtitle || ''
      }).unwrap();
      if (result?.data?.enhancedSubtitle) {
        formRef.current.setValue('subtitle', result.data.enhancedSubtitle);
        toast.success('Subtitle enhanced successfully!');
      } else {
        toast.error('No enhancement received');
      }
    } catch (error) {
      console.error('Subtitle enhancement error:', error);
      toast.error('Failed to enhance subtitle');
    }
  }, [title, subtitle, enhanceSubtitle]);

  const handleEnhanceDescription = useCallback(async () => {
    if (!title?.trim()) {
      toast.error('Please enter a title first');
      return;
    }

    try {
      const result = await enhanceDescription({
        title,
        subtitle: subtitle || '',
        description: description || ''
      }).unwrap();
      if (result?.data?.enhancedDescription) {
        formRef.current.setValue('description', result.data.enhancedDescription);
        toast.success('Description enhanced successfully!');
      } else {
        toast.error('No enhancement received');
      }
    } catch (error) {
      console.error('Description enhancement error:', error);
      toast.error('Failed to enhance description');
    }
  }, [title, subtitle, description, enhanceDescription]);

  const handleSuggestCategory = useCallback(async () => {
    if (!title?.trim()) {
      toast.error('Please enter a title first');
      return;
    }

    try {
      const result = await suggestCategory({
        title,
        description: description || ''
      }).unwrap();
      if (result?.data?.categoryId && result?.data?.subcategoryId) {
        formRef.current.setValue('categoryId', result.data.categoryId);
        formRef.current.setValue('subcategoryId', result.data.subcategoryId);
        toast.success(`Category suggested with ${Math.round((result.data.confidence || 0) * 100)}% confidence!`);
      } else {
        toast.error('No category suggestion received');
      }
    } catch (error) {
      console.error('Category suggestion error:', error);
      toast.error('Failed to suggest category');
    }
  }, [title, description, suggestCategory]);

  // Course publishing handler
  const publishCourse = useCallback(async () => {
    try {
      const values = formRef.current.getValues();
      const formData = new FormData();

      // Handle coursePrice
      if (values.coursePrice) {
        const price = Number(values.coursePrice);
        if (!isNaN(price) && price > 0) {
          formData.append("coursePrice", price.toString());
        }
      }

      // Handle file upload
      if (values.courseThumbnail instanceof File) {
        formData.append("file", values.courseThumbnail);
      }

      // Set publication status
      formData.append("isPublished", JSON.stringify(values.status === "published"));
      formData.append("status", values.status || "draft");

      // Add all form fields
      const fieldsToAdd = [
        'title', 'subtitle', 'description', 'categoryId', 'subcategoryId',
        'courseLevel', 'isFree', 'prerequisites', 'targetAudience',
        'estimatedDuration', 'language'
      ];

      fieldsToAdd.forEach(field => {
        const value = values[field as keyof typeof values];
        if (value) {
          formData.append(field, value.toString());
        }
      });

      // Add learning objectives
      const validObjectives = learningObjectives.filter(obj => obj.trim() !== '');
      if (validObjectives.length > 0) {
        formData.append("learningObjectives", JSON.stringify(validObjectives));
      }

      // Add boolean fields
      formData.append("hasSubtitles", JSON.stringify(values.hasSubtitles || false));
      formData.append("hasCertificate", JSON.stringify(values.hasCertificate || true));

      const res = await createCourse({
        id: teacherId,
        data: formData,
      }).unwrap();

      dispatch(setCourse(res.data));
      localStorage.removeItem("courseForm");

      toast.success(
        values.status === "published"
          ? "Course published successfully!"
          : "Course saved as draft!"
      );

      // Handle Stripe connection for paid courses
      if (values.isFree === "paid" && !hasStripeConnected) {
        setShowStripeModal(true);
      } else {
        navigate("/teacher/courses");
      }
    } catch (error: unknown) {
      console.error("Failed to create course:", error);
      const err = error as { data?: { message?: string } };
      toast.error(err?.data?.message || "Failed to create course");
    }
  }, [learningObjectives, createCourse, teacherId, dispatch, navigate, hasStripeConnected]);

  // Modern 4-Step Horizontal Stepper Component
  const ModernStepIndicator = () => (
    <div className="w-full mb-12">
      {/* Progress bar */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-3">
          <span className="text-sm font-semibold text-gray-800">
            Step {currentStep + 1} of {steps.length}
          </span>
          <span className="text-sm font-medium text-green-600">
            {Math.round(progressPercentage)}% Complete
          </span>
        </div>
        <Progress value={progressPercentage} className="h-3 bg-gray-100" />
      </div>

      {/* Modern Horizontal Stepper */}
      <div className="relative">
        {/* Desktop Layout (md and up) */}
        <div className="hidden md:flex items-start justify-between relative">
          {/* Connecting Line Background */}
          <div className="absolute top-8 left-0 right-0 h-0.5 bg-gray-200 z-0"
               style={{ left: '4rem', right: '4rem' }} />

          {steps.map((step, index) => {
            const isCompleted = completedSteps.includes(index);
            const isCurrent = index === currentStep;
            const isClickable = index <= currentStep + 1 || isCompleted;
            const StepIcon = step.icon;

            return (
              <div key={step.id} className="flex flex-col items-center relative z-10 flex-1">
                {/* Step Circle */}
                <button
                  onClick={() => isClickable && goToStep(index)}
                  disabled={!isClickable}
                  className={cn(
                    "relative flex items-center justify-center w-16 h-16 rounded-full transition-all duration-300 shadow-lg transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-green-100",
                    isCompleted
                      ? "bg-green-600 text-white shadow-green-200 hover:bg-green-700"
                      : isCurrent
                        ? "bg-green-600 text-white shadow-green-200 ring-4 ring-green-100"
                        : isClickable
                          ? "bg-white border-2 border-gray-300 text-gray-400 hover:border-green-300 hover:text-green-500 shadow-gray-100"
                          : "bg-gray-100 border-2 border-gray-200 text-gray-300 cursor-not-allowed shadow-gray-50"
                  )}
                  aria-current={isCurrent ? "step" : undefined}
                  role="button"
                  tabIndex={isClickable ? 0 : -1}
                >
                  {isCompleted ? (
                    <Check className="w-7 h-7 animate-in zoom-in duration-300" />
                  ) : (
                    <StepIcon className={cn(
                      "w-7 h-7 transition-all duration-300",
                      isCurrent && "animate-pulse"
                    )} />
                  )}
                </button>

                {/* Connecting Line (Active) */}
                {index < steps.length - 1 && (
                  <div className={cn(
                    "absolute top-8 left-16 h-0.5 transition-all duration-500 z-20",
                    "w-full",
                    isCompleted ? "bg-green-600" : "bg-transparent"
                  )} />
                )}

                {/* Step Content */}
                <div className="mt-4 text-center max-w-[140px]">
                  <h3 className={cn(
                    "text-sm font-semibold transition-all duration-300 mb-1",
                    isCurrent
                      ? "text-green-600"
                      : isCompleted
                        ? "text-green-700"
                        : "text-gray-500"
                  )}>
                    {step.title}
                  </h3>

                  {/* Active Step Underline */}
                  {isCurrent && (
                    <div className="w-8 h-0.5 bg-green-600 mx-auto mb-2 rounded-full" />
                  )}

                  <p className={cn(
                    "text-xs leading-tight transition-all duration-300",
                    isCurrent
                      ? "text-gray-700 font-medium"
                      : isCompleted
                        ? "text-green-600"
                        : "text-gray-400"
                  )}>
                    {step.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Mobile Layout (below md) */}
        <div className="md:hidden space-y-4">
          {steps.map((step, index) => {
            const isCompleted = completedSteps.includes(index);
            const isCurrent = index === currentStep;
            const isClickable = index <= currentStep + 1 || isCompleted;
            const StepIcon = step.icon;

            return (
              <div key={step.id} className="flex items-center space-x-4">
                {/* Step Circle */}
                <button
                  onClick={() => isClickable && goToStep(index)}
                  disabled={!isClickable}
                  className={cn(
                    "flex items-center justify-center w-12 h-12 rounded-full transition-all duration-300 shadow-md flex-shrink-0",
                    isCompleted
                      ? "bg-green-600 text-white"
                      : isCurrent
                        ? "bg-green-600 text-white ring-2 ring-green-100"
                        : isClickable
                          ? "bg-white border-2 border-gray-300 text-gray-400"
                          : "bg-gray-100 border-2 border-gray-200 text-gray-300 cursor-not-allowed"
                  )}
                  aria-current={isCurrent ? "step" : undefined}
                >
                  {isCompleted ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    <StepIcon className="w-5 h-5" />
                  )}
                </button>

                {/* Step Content */}
                <div className="flex-1">
                  <h3 className={cn(
                    "text-sm font-semibold mb-1",
                    isCurrent
                      ? "text-green-600"
                      : isCompleted
                        ? "text-green-700"
                        : "text-gray-500"
                  )}>
                    {step.title}
                  </h3>
                  <p className={cn(
                    "text-xs",
                    isCurrent
                      ? "text-gray-700"
                      : isCompleted
                        ? "text-green-600"
                        : "text-gray-400"
                  )}>
                    {step.description}
                  </p>
                </div>

                {/* Connecting Line for Mobile */}
                {index < steps.length - 1 && (
                  <div className="absolute left-6 mt-12 w-0.5 h-8 bg-gray-200" />
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-100/50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-12">
            <Skeleton className="h-12 w-96 mb-4 mx-auto" />
            <Skeleton className="h-6 w-80 mx-auto" />
          </div>
          <div className="space-y-8">
            <Skeleton className="h-4 w-full" />
            <div className="flex justify-center space-x-8">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex flex-col items-center">
                  <Skeleton className="w-14 h-14 rounded-full mb-3" />
                  <Skeleton className="h-4 w-20 mb-1" />
                  <Skeleton className="h-3 w-16" />
                </div>
              ))}
            </div>
            <Card className="shadow-2xl border-0 rounded-2xl">
              <CardContent className="p-8">
                <div className="space-y-6">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-32 w-full" />
                  <div className="grid grid-cols-2 gap-6">
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );

  if (!formInitialized) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-100/50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent mb-4">
              Create Your Course
            </h1>
            <p className="text-gray-600 text-xl max-w-2xl mx-auto leading-relaxed">
              Share your knowledge with the world and inspire learners globally
            </p>
          </div>

          {/* Step Indicator */}
          <ModernStepIndicator />

          {/* Main Content */}
          <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden">
            <CardHeader className="pb-6 bg-gradient-to-r from-green-500/10 to-green-600/10 border-b border-green-100">
              <CardTitle className="flex items-center gap-3 text-2xl">
                {React.createElement(steps[currentStep].icon, { className: "w-7 h-7 text-green-600" })}
                <span className="bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
                  {steps[currentStep].title}
                </span>
              </CardTitle>
              <p className="text-gray-600 text-lg">{steps[currentStep].description}</p>
            </CardHeader>
            <CardContent className="pt-8 px-8 pb-8">
              <Form {...form}>
                {renderStepContent()}
              </Form>
            </CardContent>
          </Card>

          {/* Stripe Modal */}
          {renderStripeModal()}
        </div>
      </div>
    </div>
  );

  // Step content renderer
  function renderStepContent() {
    switch (currentStep) {
      case 0:
        return renderBasicsStep();
      case 1:
        return renderContentStep();
      case 2:
        return renderSettingsStep();
      case 3:
        return renderReviewStep();
      default:
        return null;
    }
  }

  // Step 1: Course Basics
  function renderBasicsStep() {
    return (
      <div className="space-y-8">
        {/* Course Title */}
        <AIEnhancementField
          label="Course Title*"
          value={title || ''}
          onChange={(value) => form.setValue('title', value)}
          onEnhance={handleEnhanceTitle}
          isEnhancing={isEnhancingTitle}
          placeholder="e.g., Complete JavaScript Course 2024"
          error={form.formState.errors.title?.message}
        />

        {/* Course Subtitle */}
        <AIEnhancementField
          label="Course Subtitle"
          value={subtitle || ''}
          onChange={(value) => form.setValue('subtitle', value)}
          onEnhance={handleEnhanceSubtitle}
          isEnhancing={isEnhancingSubtitle}
          placeholder="e.g., Master JavaScript with hands-on projects"
          error={form.formState.errors.subtitle?.message}
        />

        {/* Course Description */}
        <AIEnhancementField
          label="Course Description*"
          value={description || ''}
          onChange={(value) => form.setValue('description', value)}
          onEnhance={handleEnhanceDescription}
          isEnhancing={isEnhancingDescription}
          placeholder="Describe what students will learn in this course..."
          type="textarea"
          rows={6}
          error={form.formState.errors.description?.message}
        />

        {/* Category Selection */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <CategorySelector
            selectedCategoryId={categoryId}
            selectedSubcategoryId={subcategoryId}
            onCategoryChange={(categoryId) => form.setValue('categoryId', categoryId)}
            onSubcategoryChange={(subcategoryId) => form.setValue('subcategoryId', subcategoryId)}
            onAISuggest={handleSuggestCategory}
            isAISuggesting={isSuggestingCategory}
            error={form.formState.errors.categoryId?.message || form.formState.errors.subcategoryId?.message}
          />

          {/* Course Level */}
          <FormField
            control={form.control}
            name="courseLevel"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base font-semibold">Course Level*</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="h-12">
                      <SelectValue placeholder="Select course level" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {COURSE_LEVEL.map((level) => (
                      <SelectItem key={level} value={level}>
                        <div className="flex items-center gap-2">
                          <Target className="w-4 h-4" />
                          {level}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Navigation */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate("/teacher/courses")}
            className="px-8"
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={() => {
              form
                .trigger(['title', 'description', 'categoryId', 'subcategoryId', 'courseLevel'])
                .then((isValid) => {
                  if (isValid) goToNextStep();
                });
            }}
            className="px-8 bg-green-600 hover:bg-green-700 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            Next Step
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  // Step 2: Course Content
  function renderContentStep() {
    return (
      <div className="space-y-8">
        {/* Course Thumbnail */}
        <FormField
          control={form.control}
          name="courseThumbnail"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base font-semibold">Course Thumbnail</FormLabel>
              <FormControl>
                <StableFileUpload
                  value={field.value}
                  onValueChange={field.onChange}
                  accept="image/*"
                  maxSize={5 * 1024 * 1024}
                  placeholder="Upload Course Thumbnail"
                  description="Drag and drop or"
                />
              </FormControl>
              <FormDescription>
                Upload a high-quality thumbnail that represents your course content.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Learning Objectives */}
        <div>
          <Label className="text-base font-semibold mb-4 block">
            Learning Objectives* (3-8 objectives)
          </Label>
          <div className="space-y-3">
            {learningObjectives.map((objective, index) => (
              <div key={index} className="flex gap-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mt-1">
                  <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                </div>
                <div className="flex-1">
                  <Input
                    value={objective}
                    onChange={(e) => updateLearningObjective(index, e.target.value)}
                    placeholder={`Learning objective ${index + 1}`}
                    className="h-12"
                  />
                </div>
                {learningObjectives.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => removeLearningObjective(index)}
                    className="flex-shrink-0 mt-1"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>
          {learningObjectives.length < 8 && (
            <Button
              type="button"
              variant="outline"
              onClick={addLearningObjective}
              className="mt-3"
            >
              <span className="mr-2">+</span>
              Add Learning Objective
            </Button>
          )}
        </div>

        {/* Additional Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="prerequisites"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base font-semibold">Prerequisites</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="What should students know before taking this course?"
                    className="min-h-[100px]"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="targetAudience"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base font-semibold">Target Audience</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Who is this course designed for?"
                    className="min-h-[100px]"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Navigation */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={goToPrevStep}
            className="px-8"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            type="button"
            onClick={goToNextStep}
            className="px-8 bg-green-600 hover:bg-green-700 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            Next Step
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  // Step 3: Pricing & Settings
  function renderSettingsStep() {
    return (
      <div className="space-y-8">
        {/* Pricing Section */}
        <Card className="border-2 border-blue-100 bg-blue-50/30">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-blue-600" />
              Course Pricing
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="isFree"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="grid grid-cols-1 md:grid-cols-2 gap-4"
                    >
                      <div className="flex items-center space-x-3 p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                        <RadioGroupItem value="free" id="free" />
                        <div className="flex-1">
                          <Label htmlFor="free" className="text-base font-medium cursor-pointer">
                            Free Course
                          </Label>
                          <p className="text-sm text-gray-500">
                            Make your course available to everyone
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                        <RadioGroupItem value="paid" id="paid" />
                        <div className="flex-1">
                          <Label htmlFor="paid" className="text-base font-medium cursor-pointer">
                            Paid Course
                          </Label>
                          <p className="text-sm text-gray-500">
                            Set a price for your course
                          </p>
                        </div>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {isFree === "paid" && (
              <FormField
                control={form.control}
                name="coursePrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-semibold">Course Price (USD)*</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                        <Input
                          type="number"
                          placeholder="29.99"
                          className="pl-10 h-12 text-lg"
                          min="1"
                          step="0.01"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Set a competitive price for your course content
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </CardContent>
        </Card>

        {/* Course Settings */}
        <Card className="border-2 border-purple-100 bg-purple-50/30">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-purple-600" />
              Course Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="estimatedDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-semibold">Estimated Duration</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                        <Input
                          {...field}
                          placeholder="e.g., 10 hours"
                          className="pl-10 h-12"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="language"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-semibold">Course Language</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-12">
                          <Globe className="w-5 h-5 text-gray-400 mr-2" />
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="English">English</SelectItem>
                        <SelectItem value="Spanish">Spanish</SelectItem>
                        <SelectItem value="French">French</SelectItem>
                        <SelectItem value="German">German</SelectItem>
                        <SelectItem value="Chinese">Chinese</SelectItem>
                        <SelectItem value="Japanese">Japanese</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Feature Toggles */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="hasSubtitles"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base font-medium">
                        Subtitles Available
                      </FormLabel>
                      <FormDescription>
                        Course includes subtitles for better accessibility
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="hasCertificate"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base font-medium">
                        Certificate of Completion
                      </FormLabel>
                      <FormDescription>
                        Students receive a certificate upon course completion
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {/* Publication Status */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold">Publication Status</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="h-12">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="draft">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                          Draft - Save for later
                        </div>
                      </SelectItem>
                      <SelectItem value="published">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          Published - Make live immediately
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={goToPrevStep}
            className="px-8"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            type="button"
            onClick={() => {
              form
                .trigger(['status', 'isFree', 'coursePrice'])
                .then((isValid) => {
                  if (isValid) goToNextStep();
                });
            }}
            className="px-8 bg-blue-600 hover:bg-blue-700"
          >
            Review Course
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  // Step 4: Review & Publish
  function renderReviewStep() {
    const validObjectives = learningObjectives.filter(obj => obj.trim() !== '');

    return (
      <div className="space-y-8">
        {/* Course Overview */}
        <Card className="border-2 border-green-100 bg-green-50/30">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5 text-green-600" />
              Course Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">{title}</h3>
              {subtitle && (
                <p className="text-lg text-gray-600 mb-4">{subtitle}</p>
              )}
              <div className="flex flex-wrap gap-2 mb-4">
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {selectedCategoryName}
                </Badge>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {selectedSubcategoryName}
                </Badge>
                <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                  {form.watch('courseLevel')}
                </Badge>
              </div>
              <p className="text-gray-700 leading-relaxed">{description}</p>
            </div>

            {/* Course Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="p-4 bg-white rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="w-4 h-4 text-green-600" />
                  <span className="font-medium">Price</span>
                </div>
                <p className="text-lg font-bold">
                  {isFree === "free" ? "Free" : `$${coursePrice}`}
                </p>
              </div>

              <div className="p-4 bg-white rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4 text-blue-600" />
                  <span className="font-medium">Duration</span>
                </div>
                <p className="text-lg font-bold">
                  {form.watch('estimatedDuration') || 'Not specified'}
                </p>
              </div>

              <div className="p-4 bg-white rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Globe className="w-4 h-4 text-purple-600" />
                  <span className="font-medium">Language</span>
                </div>
                <p className="text-lg font-bold">
                  {form.watch('language')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Learning Objectives */}
        {validObjectives.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-600" />
                What You'll Learn
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {validObjectives.map((objective, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                      <Check className="w-3 h-3 text-blue-600" />
                    </div>
                    <p className="text-gray-700">{objective}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Course Features */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="w-5 h-5 text-yellow-600" />
              Course Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <div className={`w-4 h-4 rounded-full ${form.watch('hasSubtitles') ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                <span className={form.watch('hasSubtitles') ? 'text-gray-900' : 'text-gray-500'}>
                  Subtitles Available
                </span>
              </div>
              <div className="flex items-center gap-3">
                <div className={`w-4 h-4 rounded-full ${form.watch('hasCertificate') ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                <span className={form.watch('hasCertificate') ? 'text-gray-900' : 'text-gray-500'}>
                  Certificate of Completion
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Publication Status */}
        <Card className={`border-2 ${status === 'published' ? 'border-green-200 bg-green-50/30' : 'border-amber-200 bg-amber-50/30'}`}>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${status === 'published' ? 'bg-green-500' : 'bg-amber-500'}`}></div>
              <span className="text-lg font-medium">
                {status === 'published' ? 'Ready to Publish' : 'Save as Draft'}
              </span>
            </div>
            <p className="text-gray-600 mt-2">
              {status === 'published'
                ? 'Your course will be published and available to students immediately.'
                : 'Your course will be saved as a draft and can be published later.'
              }
            </p>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={goToPrevStep}
            className="px-8"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            onClick={publishCourse}
            disabled={isLoading}
            className={`px-8 ${status === 'published' ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'}`}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                {status === 'published' ? 'Publishing...' : 'Saving...'}
              </div>
            ) : (
              <>
                {status === 'published' ? (
                  <>
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Publish Course
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Save Draft
                  </>
                )}
              </>
            )}
          </Button>
        </div>
      </div>
    );
  }

  // Stripe Modal Renderer
  function renderStripeModal() {
    const ModalComponent = isMobile ? Drawer : Dialog;
    const ContentComponent = isMobile ? DrawerContent : DialogContent;
    const HeaderComponent = isMobile ? DrawerHeader : DialogHeader;
    const TitleComponent = isMobile ? DrawerTitle : DialogTitle;
    const DescriptionComponent = isMobile ? DrawerDescription : DialogDescription;
    const FooterComponent = isMobile ? DrawerFooter : DialogFooter;

    return (
      <ModalComponent open={showStripeModal} onOpenChange={setShowStripeModal}>
        <ContentComponent className={isMobile ? "" : "sm:max-w-md"}>
          <HeaderComponent className="text-center">
            <TitleComponent className="text-xl font-bold">
              Connect Stripe to Receive Payments
            </TitleComponent>
            <DescriptionComponent className="text-gray-600">
              To receive payments for your courses, you need to connect your Stripe account.
              This is a one-time setup process.
            </DescriptionComponent>
          </HeaderComponent>

          <div className="px-4 py-6">
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Benefits of connecting Stripe:</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-500" />
                  Receive payments directly to your bank account
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-500" />
                  Track earnings in real-time
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-500" />
                  Manage payouts and transactions
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-500" />
                  Secure and trusted payment processing
                </li>
              </ul>
            </div>
          </div>

          <FooterComponent className="flex flex-col-reverse sm:flex-row gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowStripeModal(false);
                navigate("/teacher/courses");
              }}
              className="w-full sm:w-auto"
            >
              Skip for now
            </Button>
            <Button
              onClick={handleConnectStripe}
              disabled={isConnectingStripe}
              className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700"
            >
              {isConnectingStripe ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Connecting...
                </div>
              ) : (
                <>
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Connect with Stripe
                </>
              )}
            </Button>
          </FooterComponent>
        </ContentComponent>
      </ModalComponent>
    );
  }
};

export default React.memo(CourseCreate);
